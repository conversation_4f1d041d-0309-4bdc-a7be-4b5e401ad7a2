const path = require('path');
const CopyPlugin = require('copy-webpack-plugin');
const WebpackObfuscator = require('webpack-obfuscator');

module.exports = {
  mode: 'production',
  entry: {
    content: [
      './extupdated/content.js',
      './extupdated/utils.js',
      './extupdated/table-utils.js',
      './extupdated/api-utils.js',
      './extupdated/filter-utils.js',
      './extupdated/modal-utils.js'
      // Injector is not in entry
    ]
  },
  output: {
    filename: 'content.bundle.js', // Changed filename
    path: path.resolve(__dirname, 'dist'),
    clean: true
  },
  module: {
    rules: [
      {
        test: /\.css$/,
        use: ['style-loader', 'css-loader']
      }
    ]
  },
  optimization: {
    // minimize: false, // Keep minimization enabled if obfuscator is disabled
    splitChunks: false
  },
  plugins: [
    new CopyPlugin({
      patterns: [
        { 
          from: "extupdated/manifest.json",
          to: "[name][ext]"
        },
        { 
          from: "extupdated/icons",
          to: "icons"
        }
      ],
    }),
    new WebpackObfuscator({
      compact: true,
      controlFlowFlattening: true,
      controlFlowFlatteningThreshold: 1,
      deadCodeInjection: true,
      deadCodeInjectionThreshold: 1,
      disableConsoleOutput: false,
      identifierNamesGenerator: 'hexadecimal',
      log: false,
      numbersToExpressions: true,
      renameGlobals: true,
      rotateStringArray: true,
      shuffleStringArray: true,
      simplify: true,
      splitStrings: true,
      splitStringsChunkLength: 3,
      stringArray: true,
      stringArrayEncoding: ['base64'],
      stringArrayThreshold: 1,
      transformObjectKeys: true,
      unicodeEscapeSequence: true
    }, [])
  ],
}; 