// Объект для хранения счетчиков повторяющихся сообщений
let messageCounters = {};
let isGroupStarted = false;
let batchOperations = {
    newRows: 0,
    checkboxes: 0,
    currentPage: 0,
    totalPages: 0,
    lastMessageType: null
};

// Функция для логирования с группировкой
export function log(...args) {
    // Полностью комментируем внутреннюю логику, чтобы ничего не выводилось
    /*
    const message = args.join(' ');
    
    // Определяем тип сообщения
    let messageType = null;
    
    if (message.includes('Добавлен чекбокс для строки с ID:')) {
        messageType = 'checkboxes';
        batchOperations.checkboxes++;
        batchOperations.newRows++;
        return;
    }
    
    // Если тип сообщения изменился, выводим накопленную статистику
    if (messageType !== batchOperations.lastMessageType && batchOperations.lastMessageType) {
        flushMessageCounters();
    }
    batchOperations.lastMessageType = messageType;
    
    // Отслеживаем прогресс загрузки страниц
    if (message.includes('Всего страниц:')) {
        batchOperations.totalPages = parseInt(message.split(': ')[1]);
        console.log('[BlazingBoost Extension] Начало загрузки страниц (всего: ' + batchOperations.totalPages + ')');
        return;
    }
    
    if (message.includes('Загрузка страницы')) {
        batchOperations.currentPage = parseInt(message.split('страницы ')[1]);
        // Выводим прогресс только при изменении страницы
        console.log(`[BlazingBoost Extension] Прогресс: ${batchOperations.currentPage}/${batchOperations.totalPages}`);
        // Сбрасываем счетчики при переходе на новую страницу
        flushMessageCounters();
        return;
    }
    
    // Группируем сообщения о времени
    if (message.includes('Найденное время:') || message.includes('Добавлено время:')) {
        if (!messageCounters['times']) {
            messageCounters['times'] = new Set();
        }
        const time = message.split(': ')[1];
        messageCounters['times'].add(time);
        return;
    }
    
    // Пропускаем промежуточные сообщения о добавлении строк
    if (message.includes('Добавлено') && message.includes('строк из страницы')) {
        return;
    }
    
    // Фильтруем некоторые часто повторяющиеся сообщения
    if (message.includes('Проверка загрузки:') || 
        message.includes('Найдено строк для извлечения времени:')) {
        return;
    }
    
    // Логируем важные системные сообщения
    if (message.includes('Инициализация') || 
        message.includes('Все страницы загружены') ||
        message.includes('Ошибка')) {
        // Выводим накопленную статистику перед системным сообщением
        flushMessageCounters();
        console.log('[BlazingBoost Extension]', ...args);
    }
    */
}

// Функция для вывода накопленных сообщений
function flushMessageCounters() {
    // Комментируем вывод
    /*
    if (batchOperations.checkboxes > 0 || batchOperations.newRows > 0) {
        console.log(`[BlazingBoost Extension] Обработано ${batchOperations.newRows} строк, добавлено ${batchOperations.checkboxes} чекбоксов`);
        batchOperations.checkboxes = 0;
        batchOperations.newRows = 0;
    }
    
    if (messageCounters['times']) {
        console.log(`[BlazingBoost Extension] Обработаны времена: ${Array.from(messageCounters['times']).join(', ')}`);
        messageCounters['times'] = null;
    }
    */
}

// Функция для начала группы логов
export function startLogGroup(groupName) {
    flushMessageCounters(); // Сбрасываем счетчики перед началом новой группы
    if (!isGroupStarted) {
        // console.group('[BlazingBoost Extension] ' + groupName);
        isGroupStarted = true;
    }
}

// Функция для завершения группы логов
export function endLogGroup() {
    if (isGroupStarted) {
        flushMessageCounters();
        // console.groupEnd();
        isGroupStarted = false;
    }
}

// Функция для форматирования даты
export function formatDate(dateString) {
    try {
        if (!dateString) {
            // log('Дата отсутствует');
            return 'N/A';
        }
        
        const date = new Date(dateString.replace(' ', 'T'));
        if (isNaN(date.getTime())) {
            // log('Некорректная дата:', dateString);
            return 'N/A';
        }
        
        const month = date.toLocaleString('en-US', { month: 'long' });
        const day = date.getDate();
        
        let suffix = 'th';
        if (day % 10 === 1 && day !== 11) {
            suffix = 'st';
        } else if (day % 10 === 2 && day !== 12) {
            suffix = 'nd';
        } else if (day % 10 === 3 && day !== 13) {
            suffix = 'rd';
        }
        
        const time = date.toLocaleTimeString('en-US', { 
            hour: '2-digit', 
            minute: '2-digit', 
            hour12: false 
        });
        
        return `${month} ${day}${suffix}  -  ${time}`;
    } catch (error) {
        console.error('[BB Ext] Ошибка форматирования даты:', error); // Оставляем лог ошибки
        return 'N/A';
    }
}

// Функция для отладки состояния DOM
export function debugDOMState() {
    /*
    log('=== Отладка состояния DOM ===');
    const table = document.querySelector('[data-testid="table"]');
    log('Таблица найдена:', !!table);
    
    if (table) {
        const thead = table.querySelector('thead');
        log('thead найден:', !!thead);
        
        if (thead) {
            const headerRow = thead.querySelector('[data-testid="tr"]');
            log('Строка заголовка найдена:', !!headerRow);
            
            if (headerRow) {
                const headerCells = headerRow.querySelectorAll('[data-testid="th"]');
                log('Количество ячеек в заголовке:', headerCells.length);
                log('Содержимое первой ячейки:', headerCells[0]?.textContent);
            }
        }
        
        const tbodies = table.querySelectorAll('tbody');
        log('Количество tbody:', tbodies.length);
        
        if (tbodies.length > 0) {
            const firstRow = tbodies[0].querySelector('[data-testid="tr"]');
            log('Первая строка данных найдена:', !!firstRow);
            
            if (firstRow) {
                const cells = firstRow.querySelectorAll('[data-testid="td"]');
                log('Количество ячеек в первой строке:', cells.length);
                const idCell = firstRow.querySelector('[data-testid="td"] .css-3ibdlx p');
                log('ID в первой строке:', idCell?.textContent);
            }
        }
    }
    log('=== Конец отладки ===');
    */
} 