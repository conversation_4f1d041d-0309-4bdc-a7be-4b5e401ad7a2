// console.log('[BB Ext Injector] Script injected.');

// Сохраняем оригинальные функции
const originalPushState = history.pushState;
const originalReplaceState = history.replaceState;

// Функция для отправки сообщения в content script
function notifyUrlChange() {
    // console.log(`[BB Ext Injector] Notifying URL change: ${window.location.href}`);
    window.postMessage({
        type: 'BB_EXT_URL_CHANGE',
        url: window.location.href
    }, '*'); // Отправляем сообщение в window
}

// Перехватываем pushState
history.pushState = function() {
    originalPushState.apply(history, arguments);
    notifyUrlChange();
};

// Перехватываем replaceState (на всякий случай, некоторые SPA используют его для навигации)
history.replaceState = function() {
    originalReplaceState.apply(history, arguments);
    notifyUrlChange();
};

// Слушаем popstate (кнопки "назад"/"вперед")
window.addEventListener('popstate', notifyUrlChange);

// Слушаем hashchange (если используется)
window.addEventListener('hashchange', notifyUrlChange);

// Уведомляем об инициализации и текущем URL сразу после внедрения
// console.log('[BB Ext Injector] Initial URL notification.');
notifyUrlChange(); 