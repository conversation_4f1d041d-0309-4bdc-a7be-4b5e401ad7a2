.bb-checkbox-column {
  width: 40px !important;
  text-align: center !important;
  padding: 8px !important;
  vertical-align: middle !important;
  background: transparent !important;
}

.bb-checkbox {
  width: 18px !important;
  height: 18px !important;
  cursor: pointer !important;
  accent-color: #4D7FF5 !important;
  border: 2px solid #4D7FF5 !important;
  border-radius: 3px !important;
  position: relative !important;
  top: 2px !important;
  margin: 0 !important;
  opacity: 0.8 !important;
  transition: opacity 0.2s ease !important;
}

.bb-checkbox:hover {
  opacity: 1 !important;
}

.bb-checkbox:checked {
  background-color: #4D7FF5 !important;
  opacity: 1 !important;
}

/* Стили для темной темы таблицы */
[data-testid="table"] .bb-checkbox-column {
  color: #fff !important;
  border-color: rgba(255, 255, 255, 0.1) !important;
}

/* Стили для строк с четным/нечетным индексом */
[data-testid="tr"]:nth-child(odd) .bb-checkbox-column {
  background-color: rgba(255, 255, 255, 0.03) !important;
}

[data-testid="tr"]:nth-child(even) .bb-checkbox-column {
  background-color: transparent !important;
}

.bb-delete-button {
  display: none !important;
}

/* Стили для discounted сервиса */
.css-rwosof {
  color: #4ade80 !important; /* Зеленый цвет для [Discounted] */
  font-weight: 600 !important;
}

/* Стили для модального окна */
.css-wp0xt3 {
    font-family: 'Rajdhani', sans-serif;
}

.css-1j1a9hd {
    font-size: 24px;
    font-weight: 600;
    margin: 0 0 20px;
    padding-right: 40px;
}

.css-lyeuba {
    position: absolute;
    top: 20px;
    right: 20px;
    background: none;
    border: none;
    cursor: pointer;
    padding: 0;
}

.css-lyeuba svg {
    width: 20px;
    height: 20px;
    fill: #666;
}

.css-1vlottr {
    max-height: calc(100vh - 200px);
    overflow-y: auto;
}

.css-1gek93m {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.css-1cw614e {
    display: flex;
    align-items: center;
    gap: 12px;
}

.css-1hlssd1,
.css-agna0h {
    min-width: 150px;
    color: #666;
}

.css-1fe8tda,
.css-18fknow,
.css-1ooh6uu {
    color: #333;
    font-weight: 500;
}

/* Анимация для модального окна */
@keyframes modalFadeIn {
    from {
        opacity: 0;
        transform: translate(-50%, -48%);
    }
    to {
        opacity: 1;
        transform: translate(-50%, -50%);
    }
}

.css-wp0xt3 {
    animation: modalFadeIn 0.2s ease-out;
}

/* Обновленные стили для панели фильтров */
.bb-filter-panel {
    margin: 20px 0;
    padding: 20px;
    background: rgb(24, 26, 27);
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.bb-filter-container {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
    margin-bottom: 20px;
}

.bb-filter-section {
    min-width: 200px;
    flex: 1;
    margin-bottom: 15px;
}

.bb-filter-title {
    color: #fff;
    font-size: 14px;
    font-weight: 600;
    margin-bottom: 10px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.bb-filter-checkboxes {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.bb-filter-checkbox {
    width: 16px !important;
    height: 16px !important;
    cursor: pointer !important;
    accent-color: #4D7FF5 !important;
    border: 2px solid #4D7FF5 !important;
    border-radius: 3px !important;
    position: relative !important;
    margin: 0 !important;
    opacity: 0.8 !important;
    transition: all 0.2s ease !important;
    background-color: rgb(35, 37, 38) !important;
    appearance: none !important;
    -webkit-appearance: none !important;
    -moz-appearance: none !important;
}

.bb-filter-checkbox:hover {
    opacity: 1 !important;
    border-color: #8274F6 !important;
}

.bb-filter-checkbox:checked {
    background-color: #4D7FF5 !important;
    opacity: 1 !important;
}

.bb-filter-checkbox:checked::after {
    content: '✓' !important;
    position: absolute !important;
    top: 50% !important;
    left: 50% !important;
    transform: translate(-50%, -50%) !important;
    color: white !important;
    font-size: 12px !important;
    font-weight: bold !important;
}

.bb-filter-checkbox-label {
    display: flex !important;
    align-items: center !important;
    gap: 8px !important;
    color: #fff !important;
    cursor: pointer !important;
    padding: 4px !important;
    border-radius: 4px !important;
    transition: background-color 0.2s ease !important;
}

.bb-filter-checkbox-label:hover {
    background-color: rgba(77, 127, 245, 0.1) !important;
}

.bb-filter-checkbox-label span {
    font-size: 14px !important;
    user-select: none !important;
}

.bb-date-inputs {
    display: flex;
    gap: 10px;
    margin-bottom: 20px;
    justify-content: space-between;
}

.bb-date-group {
    display: flex;
    flex-direction: column;
    gap: 5px;
    width: 48%;
}

.bb-date-group label {
    color: #fff;
    font-size: 12px;
}

.bb-date-input {
    padding: 8px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 4px;
    background: rgb(35, 37, 38);
    color: #fff;
    font-family: 'Rajdhani', sans-serif;
    font-size: 14px;
}

.bb-date-input:hover {
    border-color: rgba(255, 255, 255, 0.2);
}

.bb-date-input:focus {
    outline: none;
    border-color: #4D7FF5;
}

.bb-sort-controls {
    display: flex;
    gap: 10px;
    margin-top: 10px;
    justify-content: flex-end;
}

.bb-filter-controls {
    display: flex;
    gap: 10px;
    margin-top: 20px;
}

.bb-filter-button {
    padding: 8px 16px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-family: 'Rajdhani', sans-serif;
    font-weight: 600;
    font-size: 14px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    transition: all 0.3s ease;
}

.bb-filter-button:disabled {
    cursor: not-allowed;
    opacity: 0.7;
    transform: none;
}

.bb-filter-button:hover:not(:disabled) {
    opacity: 0.9;
    transform: translateY(-1px);
}

.bb-filter-button.primary {
    background: linear-gradient(to right, #4D7FF5, #8274F6);
    color: white;
}

.bb-filter-button.danger {
    background: linear-gradient(to right, #F54D4D, #F68274);
    color: white;
    display: none;
}

.bb-filter-button.danger.visible {
    display: inline-block;
}

.bb-filter-button.secondary {
    background: rgb(35, 37, 38);
    color: white;
    border: 1px solid #444;
}

/* Стили для чекбокса автозагрузки */
.bb-autoload-label {
    display: flex !important;
    align-items: center !important;
    gap: 8px !important;
    margin-right: 15px !important;
    color: #fff !important;
    cursor: pointer !important;
    padding: 4px !important;
    border-radius: 4px !important;
    transition: background-color 0.2s ease !important;
}

.bb-autoload-label:hover {
    background-color: rgba(77, 127, 245, 0.1) !important;
}

.bb-autoload-label span {
    font-size: 14px !important;
    user-select: none !important;
    white-space: nowrap !important;
}

/* Стили для фильтра времени */
.bb-time-filter {
    margin-top: 10px;
    position: relative;
}

.bb-time-button {
    width: 100%;
    padding: 8px;
    background: rgb(35, 37, 38);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 4px;
    color: #fff;
    cursor: pointer;
    text-align: left;
    font-family: 'Rajdhani', sans-serif;
    font-size: 14px;
}

.bb-time-button:hover {
    border-color: rgba(255, 255, 255, 0.2);
}

.bb-time-dropdown {
    display: none;
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: rgb(35, 37, 38);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 4px;
    margin-top: 4px;
    max-height: 300px;
    overflow-y: auto;
    z-index: 1000;
}

.bb-time-dropdown.show {
    display: block;
}

.bb-time-header {
    padding: 8px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.bb-time-list {
    padding: 8px;
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 4px;
}

.bb-time-list .bb-filter-checkbox-label {
    padding: 4px 8px;
} 