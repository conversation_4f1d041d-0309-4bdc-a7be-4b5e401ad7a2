import * as utils from './utils.js';
import * as tableUtils from './table-utils.js';
import * as apiUtils from './api-utils.js';
import * as filterUtils from './filter-utils.js';
import * as modalUtils from './modal-utils.js';
import './styles.css';

// Целевой URL, на котором должно работать расширение
const TARGET_URL = 'https://blazingboost.com/booster/wow-raid-runs';

// Флаг для отслеживания, инициализировано ли расширение
let extensionInitialized = false;

// Сохраняем наблюдатель DOM в глобальной переменной
let domObserver = null;

// Сохраняем список обработчиков событий для последующего удаления
let eventListeners = [];

// Сохраняем текущий URL для отслеживания изменений
let currentUrl = window.location.href;

// Функция для безопасного добавления обработчиков событий с возможностью их последующего удаления
function addTrackedEventListener(element, eventType, handler, options) {
    // console.log(`[BB Ext] addTrackedEventListener: Adding listener for ${eventType} on`, element);
    element.addEventListener(eventType, handler, options);
    eventListeners.push({ element, eventType, handler, options });
}

// Функция для проверки текущего URL
function isTargetUrl() {
    const currentUrl = window.location.href;
    const result = currentUrl.startsWith(TARGET_URL);
    // console.log(`[BB Ext] isTargetUrl called. Current URL: ${currentUrl}, Target URL: ${TARGET_URL}, Result: ${result}`);
    return result;
}

// Функция для удаления интерфейса расширения
function removeExtensionInterface() {
    if (!extensionInitialized) return;
    
    // Отключаем наблюдатель DOM, если он активен
    if (domObserver) {
        domObserver.disconnect();
        domObserver = null;
    }
    
    // Удаляем все зарегистрированные обработчики событий
    eventListeners.forEach(({ element, eventType, handler, options }) => {
        try {
            element.removeEventListener(eventType, handler, options);
        } catch (error) {
            console.error('[BB Ext] Ошибка при удалении обработчика события:', error);
        }
    });
    eventListeners = [];
    
    // Удаляем все добавленные элементы
    document.querySelectorAll('.bb-checkbox-column, .bb-checkbox, .bb-filter-panel, .bb-delete-button, .bb-load-more-button, #autoloadCheckbox, #loadAllPages, #deleteSelected').forEach(el => {
        try {
            el.remove();
        } catch (error) {
            console.error('[BB Ext] Ошибка при удалении элемента:', error);
        }
    });
    
    // Удаляем модальное окно и оверлей, если они есть
    document.querySelectorAll('.css-wp0xt3, [style*="background: rgba(0, 0, 0, 0.7)"]').forEach(el => {
        try {
            el.remove();
        } catch (error) {
            console.error('[BB Ext] Ошибка при удалении модального окна:', error);
        }
    });
    
    // Сбрасываем флаг инициализации
    extensionInitialized = false;
}

// Функция для проверки URL и управления интерфейсом
function checkUrlAndManageInterface() {
    if (isTargetUrl()) {
        if (!extensionInitialized) {
            initializeExtension().catch(error => {
                console.error('[BB Ext] Ошибка при инициализации:', error);
            });
        }
    } else {
        removeExtensionInterface();
    }
}

// --- Прямой полинг URL и обработчики стандартных событий навигации ---

// 1. Регулярная проверка URL с помощью setInterval
function startUrlPolling() {
    // Проверка URL каждые 500 мс - это наш основной механизм
    setInterval(() => {
        const newUrl = window.location.href;
        if (newUrl !== currentUrl) {
            currentUrl = newUrl;
            // console.log(`[BB Ext] URL changed by polling: ${newUrl}`);
            checkUrlAndManageInterface();
        }
    }, 500);
    
    // 2. Слушаем стандартные события навигации, которые не блокируются CSP
    addTrackedEventListener(window, 'popstate', () => {
        // console.log(`[BB Ext] 'popstate' event detected, URL: ${window.location.href}`);
        currentUrl = window.location.href;
        checkUrlAndManageInterface();
    });
    
    addTrackedEventListener(window, 'hashchange', () => {
        // console.log(`[BB Ext] 'hashchange' event detected, URL: ${window.location.href}`);
        currentUrl = window.location.href;
        checkUrlAndManageInterface();
    });
    
    // 3. Следим за изменениями в DOM, особенно за загрузкой частей страницы
    const domWatcher = new MutationObserver((mutations) => {
        // Проверяем URL после значительных изменений DOM
        if (mutations.some(m => m.addedNodes && m.addedNodes.length > 3)) {
            const newUrl = window.location.href;
            if (newUrl !== currentUrl) {
                currentUrl = newUrl;
                // console.log(`[BB Ext] URL change detected via DOM: ${newUrl}`);
                checkUrlAndManageInterface();
            }
        }
    });
    
    domWatcher.observe(document.body, {
        childList: true,
        subtree: true
    });
    
    // Запускаем первичную проверку сразу
    checkUrlAndManageInterface();
}

function addDeleteButton() {
    // utils.log('Добавляем функциональную кнопку удаления');
    
    // Удаляем существующую кнопку, если она есть
    const existingButton = document.querySelector('.bb-delete-button');
    if (existingButton) {
        existingButton.remove();
    }

    const button = document.createElement('button');
    button.className = 'bb-delete-button';
    button.style.cssText = `
        visibility: hidden;
        position: fixed;
        bottom: 20px;
        right: 20px;
        padding: 10px 20px;
        background: linear-gradient(to right, #F54D4D, #F68274);
        color: white;
        border: none;
        border-radius: 4px;
        cursor: pointer;
        font-size: 14px;
        z-index: 1000;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    `;
    button.textContent = 'Удалить выбранные';
    document.body.appendChild(button);
    // utils.log('Функциональная кнопка удаления добавлена');
    return button;
}
	
function addLoadMoreButton() {
    // utils.log('Добавляем кнопку "Загрузить еще"');
    
    // Удаляем существующую кнопку, если она есть
    const existingButton = document.querySelector('.bb-load-more-button');
    if (existingButton) {
        existingButton.remove();
    }

    const button = document.createElement('button');
    button.className = 'bb-load-more-button';
    button.style.display = 'none';
    button.textContent = 'Загрузить все страницы';
    
    document.body.appendChild(button);
    
    // Обновляем обработчик клика
    addTrackedEventListener(button, 'click', async () => { // Используем addTrackedEventListener
        button.disabled = true;
        
        const filterButton = document.querySelector('#loadAllPages');
        if (filterButton) {
            filterButton.disabled = true;
            filterButton.textContent = 'Загрузка...';
        }
        
        try {
            const difficulties = ['normal', 'heroic', 'mythic'];
            let allItems = [];

            const table = document.querySelector('[data-testid="table"]');
            if (!table) {
                throw new Error('Таблица не найдена');
            }

            // Очищаем существующие строки
            const existingTbodies = table.querySelectorAll('tbody');
            existingTbodies.forEach(tbody => tbody.remove());

            for (const difficulty of difficulties) {
                const firstPageData = await apiUtils.loadPageData(1, difficulty);

                if (!firstPageData || !firstPageData.items || firstPageData.items.length === 0) {
                    continue; // Пропускаем, если у сложности нет данных
                }

                allItems = allItems.concat(firstPageData.items);

                const totalItems = firstPageData.totalCount;
                const itemsPerPage = firstPageData.limit;

                if (!totalItems || !itemsPerPage || totalItems <= itemsPerPage) {
                    continue; // Только одна страница, уже загрузили
                }

                const totalPages = Math.ceil(totalItems / itemsPerPage);
                const pagePromises = [];
                for (let page = 2; page <= totalPages; page++) {
                    pagePromises.push(apiUtils.loadPageData(page, difficulty));
                }

                if (pagePromises.length > 0) {
                    const results = await Promise.all(pagePromises);
                    const newItems = results.flatMap(result => result?.items || []);
                    allItems = allItems.concat(newItems);
                }
            }

            // Создаем DocumentFragment для пакетного добавления
            const fragment = document.createDocumentFragment();
            allItems.forEach(run => {
                const tbody = tableUtils.createTableRow(run);
                if (tbody) {
                    fragment.appendChild(tbody);
                }
            });

            // Добавляем фрагмент в таблицу
            table.appendChild(fragment);
            
            // Удаляем пагинацию
            const pagination = document.querySelector('.MuiPagination-root');
            if (pagination) {
                pagination.remove();
            }
            
            // Скрываем кнопку в фильтрах
            if (filterButton) {
                filterButton.style.display = 'none';
            }
            
            button.remove();
            
            filterUtils.setLoadingComplete(true);
            
        } catch (error) {
            console.error('[BB Ext] Ошибка при загрузке страниц:', error);
            if (filterButton) {
                filterButton.textContent = 'Ошибка загрузки';
                filterButton.disabled = false;
            }
        }
    });

    return button;
}

function addAutoloadCheckbox() {
    const container = document.querySelector('.css-u7vqju');
    if (!container) return;

    // Создаем контейнер для чекбокса
    const checkboxContainer = document.createElement('label');
    checkboxContainer.className = 'bb-autoload-label';
    
    // Получаем сохраненное состояние
    const isAutoloadEnabled = localStorage.getItem('bb-autoload-enabled') === 'true';
    
    checkboxContainer.innerHTML = `
        <input type="checkbox" class="bb-filter-checkbox" id="autoloadCheckbox" ${isAutoloadEnabled ? 'checked' : ''}>
        <span>Подгружать все страницы при открытии</span>
    `;
    
    // Вставляем перед первой ссылкой
    container.insertBefore(checkboxContainer, container.firstChild);
    
    // Добавляем обработчик изменения
    const checkbox = checkboxContainer.querySelector('#autoloadCheckbox');
    addTrackedEventListener(checkbox, 'change', (e) => {
        localStorage.setItem('bb-autoload-enabled', e.target.checked);
        
        // Если включили автозагрузку, запускаем загрузку всех страниц
        if (e.target.checked) {
            // Очищаем существующие строки перед загрузкой
            const table = document.querySelector('[data-testid="table"]');
            if (table) {
                const existingTbodies = table.querySelectorAll('tbody');
                existingTbodies.forEach(tbody => tbody.remove());
            }
            
            const loadMoreButton = document.querySelector('.bb-load-more-button');
            if (loadMoreButton) {
                loadMoreButton.click();
            }
        }
    });
    
    // Если чекбокс активен при загрузке страницы, запускаем загрузку всех страниц
    if (isAutoloadEnabled) {
        // utils.log('Автозагрузка включена, ожидаем полной загрузки страницы...');
        
        // Функция для проверки готовности страницы
        const checkPageReady = () => {
            const table = document.querySelector('[data-testid="table"]');
            const loadMoreButton = document.querySelector('.bb-load-more-button');
            
            if (!table) {
                // utils.log('Таблица еще не создана, ожидаем...');
                setTimeout(checkPageReady, 500);
                return;
            }

            // Проверяем наличие строк в таблице
            const rows = table.querySelectorAll('tbody tr');
            if (rows.length > 0) {
                // utils.log('Страница загружена, начинаем автозагрузку');
                // Очищаем существующие строки перед загрузкой
                const existingTbodies = table.querySelectorAll('tbody');
                existingTbodies.forEach(tbody => tbody.remove());
                
                // Нажимаем кнопку загрузки
                if (loadMoreButton) {
                    loadMoreButton.click();
                }
                return;
            }

            // Проверяем, есть ли сообщение о пустой таблице
            const emptyMessage = document.querySelector('.css-tg784k');
            if (emptyMessage && emptyMessage.textContent.includes("You don't have any data yet")) {
                // Делаем дополнительную проверку через секунду, чтобы убедиться, что это не временное состояние
                setTimeout(() => {
                    const updatedEmptyMessage = document.querySelector('.css-tg784k');
                    if (updatedEmptyMessage && updatedEmptyMessage.textContent.includes("You don't have any data yet")) {
                        // utils.log('Таблица действительно пуста, автозагрузка не требуется');
                    } else {
                        // Если сообщение исчезло, продолжаем проверку
                        checkPageReady();
                    }
                }, 1000);
                return;
            }

            // Если ни одно из условий не сработало, продолжаем проверку
            // utils.log('Страница еще не готова, ожидаем...');
            setTimeout(checkPageReady, 500);
        };
        
        // Запускаем проверку с начальной задержкой
        setTimeout(checkPageReady, 1000);
    }
}

async function initializeExtension() {
    if (extensionInitialized) {
        // console.log('[BB Ext] Инициализация уже выполнена.');
        return;
    }
    
    extensionInitialized = true;
    // utils.log('[BB Ext] Начало инициализации расширения...');

    try {
        // utils.startLogGroup('Инициализация расширения');
        // utils.log('Начало инициализации');
        
        // Добавляем наблюдатель за изменениями в DOM
        domObserver = new MutationObserver((mutations) => {
            for (const mutation of mutations) {
                if (mutation.addedNodes.length) {
                    // Проверяем добавление таблицы
                    const table = document.querySelector('[data-testid="table"]');
                    if (table && !table.querySelector('.bb-checkbox-column')) {
                        // utils.startLogGroup('Обработка таблицы');
                        
                        // Добавляем панель фильтров перед таблицей
                        if (!document.querySelector('.bb-filter-panel')) {
                            table.parentNode.insertBefore(filterUtils.createFilterPanel(), table);
                        }
                        
                        tableUtils.addCheckboxColumn();
                        addLoadMoreButton();
                        addAutoloadCheckbox();
                        
                        // utils.endLogGroup();
                    }

                    // Проверяем добавление новых строк
                    mutation.addedNodes.forEach(node => {
                        if (node.nodeType === 1) {
                            const rows = node.querySelectorAll?.('[data-testid="tr"]') || [];
                            if (rows.length > 0) {
                                rows.forEach(row => {
                                    if (!row.querySelector('.bb-checkbox-column')) {
                                        tableUtils.addCheckboxToRow(row);
                                    }
                                });
                            }
                        }
                    });
                }
            }
        });

        domObserver.observe(document.body, {
            childList: true,
            subtree: true
        });

        // Первоначальная инициализация
        const table = document.querySelector('[data-testid="table"]');
        if (table) {
            // utils.startLogGroup('Первоначальная инициализация');
            // Добавляем панель фильтров перед таблицей, если её ещё нет
            if (!document.querySelector('.bb-filter-panel')) {
                table.parentNode.insertBefore(filterUtils.createFilterPanel(), table);
            }
            
            tableUtils.addCheckboxColumn();
            addLoadMoreButton();
            addAutoloadCheckbox();
            // utils.endLogGroup();
        }
        
        const deleteButton = addDeleteButton();
        // utils.endLogGroup();

        // Обработчик для чекбокса "выделить все"
        addTrackedEventListener(document, 'change', (e) => {
            if (e.target.id === 'selectAll') {
                // utils.log('Нажата кнопка "Выбрать все"');
                const isChecked = e.target.checked;
                const checkboxes = document.querySelectorAll('.bb-checkbox:not(#selectAll)');
                
                checkboxes.forEach(checkbox => {
                    const row = checkbox.closest('tbody');
                    // Check if row exists AND if the inner tr is visible (using optional chaining)
                    if (row && row.querySelector('tr')?.style.display !== 'none') { 
                        checkbox.checked = isChecked;
                    }
                });
                
                // Update delete button visibility (check if button exists)
                const deleteBtnInstance = document.querySelector('.bb-delete-button'); // Get current instance
                const deleteSelectedButton = document.querySelector('#deleteSelected'); 
                const checkedBoxes = document.querySelectorAll('.bb-checkbox:checked:not(#selectAll)');
                
                if (deleteBtnInstance) {
                    deleteBtnInstance.style.visibility = checkedBoxes.length > 0 ? 'visible' : 'hidden';
                }
                if (deleteSelectedButton) {
                    deleteSelectedButton.classList.toggle('visible', checkedBoxes.length > 0);
                }
            }
        });

        // Обработчик для отдельных чекбоксов
        addTrackedEventListener(document, 'change', (e) => {
            if (e.target.classList.contains('bb-checkbox') && e.target.id !== 'selectAll') {
                const selectAllCheckbox = document.querySelector('#selectAll');
                const visibleCheckboxes = Array.from(document.querySelectorAll('.bb-checkbox:not(#selectAll)')).filter(checkbox => {
                    const row = checkbox.closest('tbody');
                    // Check if row exists AND if the inner tr exists AND is visible (using optional chaining)
                    return row && row.querySelector('tr')?.style.display !== 'none'; 
                });
                const checkedVisibleCheckboxes = visibleCheckboxes.filter(checkbox => checkbox.checked);
                
                if (selectAllCheckbox) {
                    selectAllCheckbox.checked = visibleCheckboxes.length > 0 && checkedVisibleCheckboxes.length === visibleCheckboxes.length;
                }
                
                // Update delete button visibility (check if buttons exist)
                const deleteBtnInstance = document.querySelector('.bb-delete-button'); // Get current instance
                const deleteSelectedButton = document.querySelector('#deleteSelected');
                
                if (deleteBtnInstance) {
                    deleteBtnInstance.style.visibility = checkedVisibleCheckboxes.length > 0 ? 'visible' : 'hidden';
                }
                if (deleteSelectedButton) {
                    deleteSelectedButton.classList.toggle('visible', checkedVisibleCheckboxes.length > 0);
                }
            }
        });

        // Обработчик для кнопки удаления (убедимся, что deleteButton существует)
        if (deleteButton) { // Add check here
            addTrackedEventListener(deleteButton, 'click', async () => {
                const checkedBoxes = document.querySelectorAll('.bb-checkbox:checked:not(#selectAll)');
                const ids = Array.from(checkedBoxes).map(checkbox => checkbox.dataset.id);
                
                if (confirm(`Вы уверены, что хотите удалить ${ids.length} событий?`)) {
                    for (const id of ids) {
                        const success = await apiUtils.deleteEvent(id);
                        if (success) {
                            const checkbox = document.querySelector(`input[data-id="${id}"]`);
                            const tbody = checkbox?.closest('tbody');
                            if (tbody) {
                                tbody.remove();
                            }
                        }
                    }
                    
                    // Update delete button visibility (check if buttons exist)
                    const remainingChecked = document.querySelectorAll('.bb-checkbox:checked:not(#selectAll)');
                    const deleteSelectedButton = document.querySelector('#deleteSelected');
                    const deleteBtnInstance = document.querySelector('.bb-delete-button');
                    
                    if(deleteBtnInstance) {
                        deleteBtnInstance.style.visibility = remainingChecked.length > 0 ? 'visible' : 'hidden';
                    }
                    if (deleteSelectedButton) {
                        deleteSelectedButton.classList.toggle('visible', remainingChecked.length > 0);
                    }
                }
            });
        } else {
            console.warn('[BB Ext] Delete button not found, cannot attach listener.');
        }

        // utils.log('[BB Ext] Инициализация расширения завершена.');
    } catch (error) {
        console.error('[BB Ext] Ошибка при инициализации:', error); // Оставляем лог ошибки
    }
}

// Запускаем механизм наблюдения за URL при загрузке страницы
if (document.readyState === 'loading') {
    window.addEventListener('DOMContentLoaded', startUrlPolling);
} else {
    startUrlPolling();
}

// Добавляем функцию для логирования данных API
async function debugPageData(page) {
    const data = await loadPageData(page);
    utils.log('Данные страницы:', data);
    if (data && data.data && data.data.length > 0) {
        utils.log('Пример записи:', data.data[0]);
    }
}

function createModalWindow(data) {
    // Форматируем даты
    const formatDate = (dateString) => {
        try {
            if (!dateString) {
                utils.log('Дата отсутствует');
                return 'N/A';
            }
            
            const date = new Date(dateString.replace(' ', 'T'));
            if (isNaN(date.getTime())) {
                utils.log('Некорректная дата:', dateString);
                return 'N/A';
            }
            
            const month = date.toLocaleString('en-US', { month: 'long' });
            const day = date.getDate();
            
            // Правильное определение суффикса
            let suffix = 'th';
            if (day % 10 === 1 && day !== 11) {
                suffix = 'st';
            } else if (day % 10 === 2 && day !== 12) {
                suffix = 'nd';
            } else if (day % 10 === 3 && day !== 13) {
                suffix = 'rd';
            }
            
            const time = date.toLocaleTimeString('en-US', { 
                hour: '2-digit', 
                minute: '2-digit', 
                hour12: false 
            });
            
            return `${month} ${day}${suffix}  -  ${time} ${data.runTimezone?.name || 'CET'}`;
        } catch (error) {
            utils.log('Ошибка форматирования даты:', error);
            return 'N/A';
        }
    };

    // Создаем элементы модального окна
    const modal = document.createElement('div');
    modal.className = 'css-wp0xt3';
    modal.tabIndex = '-1';
    modal.style.cssText = `
        opacity: 1;
        transition: opacity 225ms cubic-bezier(0.4, 0, 0.2, 1);
        position: fixed;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        background: rgb(24, 26, 27);
        padding: 20px;
        border-radius: 8px;
        z-index: 1001;
        min-width: 400px;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
        color: #fff;
    `;

    // Логируем данные для отладки
    utils.log('Форматирование дат:', {
        runAt: data.runAt,
        postedAt: data.postedAt,
        formattedRunAt: formatDate(data.runAt),
        formattedPostedAt: formatDate(data.postedAt)
    });

    // Добавляем заголовок и кнопку закрытия
    modal.innerHTML = `
        <h3 class="css-1j1a9hd" style="color: #fff;">View run summary</h3>
        <button class="css-lyeuba">
            <svg style="fill: #fff;"><use href="/front-ui/dist/pro/images/sprite.svg?20a1b45fe38439b1bd0ff7f58d893bc2#delete"></use></svg>
        </button>
        <div class="css-1vlottr">
            <div class="css-1gek93m">
                <div class="css-1cw614e">
                    <span class="css-1hlssd1" style="color: #999;"><strong>Date of run:</strong></span>
                    <span class="css-1fe8tda" style="color: #fff;">${formatDate(data.runAt)}</span>
                </div>
                <div class="css-1cw614e">
                    <span class="css-agna0h" style="color: #999;"><strong>Date of posting:</strong></span>
                    <span class="css-18fknow" style="color: #fff;">${formatDate(data.postedAt)}</span>
                </div>
                <div class="css-1cw614e"></div>
                <div class="css-1cw614e">
                    <span class="css-1hlssd1" style="color: #999;"><strong>Region:</strong></span>
                    <span class="css-1ooh6uu" style="color: #fff;">${data.region?.name || 'N/A'}</span>
                </div>
                <div class="css-1cw614e">
                    <span class="css-1hlssd1" style="color: #999;"><strong>Raid:</strong></span>
                    <span class="css-1ooh6uu" style="color: #fff;">${data.name || 'N/A'}</span>
                </div>
                <div class="css-1cw614e">
                    <span class="css-1hlssd1" style="color: #999;"><strong>Difficulty:</strong></span>
                    <span class="css-1ooh6uu" style="color: #fff;">${data.difficulty?.name || 'N/A'}</span>
                </div>
                <div class="css-1cw614e">
                    <span class="css-1hlssd1" style="color: #999;"><strong>Run type:</strong></span>
                    <span class="css-1ooh6uu" style="color: #fff;">${data.lootType?.name || 'N/A'}</span>
                </div>
                <div class="css-1cw614e">
                    <span class="css-1hlssd1" style="color: #999;"><strong>Service:</strong></span>
                    <span class="css-1ooh6uu" style="color: #fff;">${data.service || 'N/A'}</span>
                </div>
                <div class="css-1cw614e">
                    <span class="css-1hlssd1" style="color: #999;"><strong>Allowed Modes:</strong></span>
                    <span class="css-1ooh6uu" style="color: #fff;">${data.modes?.selfplay?.name || 'N/A'}</span>
                </div>
                <div class="css-1cw614e">
                    <span class="css-1hlssd1" style="color: #999;"><strong>Unsaved raiders:</strong></span>
                    <span class="css-1ooh6uu" style="color: #fff;">${data.unsavedRaiders || 'N/A'}</span>
                </div>
            </div>
        </div>
    `;

    // Добавляем затемнение фона
    const overlay = document.createElement('div');
    overlay.style.cssText = 'position: fixed; top: 0; left: 0; right: 0; bottom: 0; background: rgba(0, 0, 0, 0.7); z-index: 1000;';

    // Добавляем обработчики закрытия
    const closeModal = () => {
        modal.style.opacity = '0';
        overlay.style.opacity = '0';
        setTimeout(() => {
            modal.remove();
            overlay.remove();
        }, 225);
    };

    overlay.addEventListener('click', closeModal);
    modal.querySelector('.css-lyeuba').addEventListener('click', closeModal);

    // Добавляем элементы на страницу
    document.body.appendChild(overlay);
    document.body.appendChild(modal);
}

// Обновляем обработчик для кнопки просмотра
async function handleViewClick(id) {
    try {
        const eventData = await apiUtils.getEventDetails(id);
        if (eventData) {
            modalUtils.createModalWindow(eventData);
        }
    } catch (error) {
        utils.log('Ошибка при получении данных события:', error);
    }
} 