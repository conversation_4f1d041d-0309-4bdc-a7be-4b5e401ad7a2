// Импортируем утилиты логирования
import { log } from './utils.js';
import { deleteEvent, getEventDetails } from './api-utils.js';

// Функция форматирования столбца Service
function formatServiceColumn(service) {
    if (!service) return '<p>N/A</p>';

    // Проверяем, содержит ли сервис информацию о скидке
    // Ожидаем формат типа "discounted:Full Clear" или подобный
    if (typeof service === 'string' && service.toLowerCase().includes('discounted')) {
        // Если это строка с discounted, разбираем её
        const parts = service.split(':');
        if (parts.length === 2 && parts[0].toLowerCase().trim() === 'discounted') {
            const actualService = parts[1].trim();
            return `
                <p><span color="green" class="css-rwosof">[Discounted]</span></p>
                <p>${actualService}</p>
            `;
        }
    }

    // Если это объект с полем discounted
    if (typeof service === 'object' && service.discounted) {
        const actualService = service.name || service.value || 'Unknown Service';
        return `
            <p><span color="green" class="css-rwosof">[Discounted]</span></p>
            <p>${actualService}</p>
        `;
    }

    // Обычный случай - просто выводим сервис
    return `<p>${service}</p>`;
}

// Функция добавления чекбокса в строку
export function addCheckboxToRow(row) {
    if (!row || row.querySelector('.bb-checkbox-column')) {
        return;
    }

    const checkboxCell = document.createElement('td');
    checkboxCell.className = 'bb-checkbox-column';
    checkboxCell.setAttribute('data-testid', 'td');
    
    const idCell = row.querySelector('[data-testid="td"] .css-3ibdlx p');
    const id = idCell?.textContent?.trim();
    
    if (id) {
        checkboxCell.innerHTML = `<input type="checkbox" class="bb-checkbox" data-id="${id}">`;
        row.insertBefore(checkboxCell, row.firstChild);
        // log(`Добавлен чекбокс для строки с ID: ${id}`);
    }
}

// Функция добавления столбца с чекбоксами
export function addCheckboxColumn() {
    // log('=== Начало addCheckboxColumn ===');
    
    // Ищем таблицу
    const table = document.querySelector('[data-testid="table"]');
    if (!table) {
        // log('Таблица не найдена');
        return;
    }
    // log('Таблица найдена');

    // Находим строку заголовка
    const headerRow = table.querySelector('thead [data-testid="tr"]');
    if (!headerRow) {
        // log('Строка заголовка не найдена');
        return;
    }
    // log('Строка заголовка найдена');

    // Проверяем, не добавлен ли уже столбец с чекбоксами
    if (!headerRow.querySelector('.bb-checkbox-column')) {
        try {
            // Добавляем заголовок столбца с чекбоксом
            const checkboxHeader = document.createElement('th');
            checkboxHeader.className = 'bb-checkbox-column';
            checkboxHeader.setAttribute('data-testid', 'th');
            checkboxHeader.innerHTML = '<input type="checkbox" class="bb-checkbox" id="selectAll">';
            headerRow.insertBefore(checkboxHeader, headerRow.firstChild);
            // log('Добавлен заголовок с чекбоксом');
        } catch (error) {
            console.error('[BB Ext] Ошибка при добавлении заголовка чекбокса:', error); // Оставляем лог ошибки
        }
    }

    // Добавляем чекбоксы к существующим строкам
    const rows = table.querySelectorAll('tbody [data-testid="tr"]');
    rows.forEach(row => addCheckboxToRow(row));
}

// Функция для форматирования режимов
function formatModes(modes, isExpired = false, modeClass = 'css-3ibdlx') {
    if (!modes) return '<p>N/A</p>';
    
    const modeElements = [];
    if (modes.selfplay) {
        if (isExpired) {
            modeElements.push(`<div class="${modeClass}"><p>${modes.selfplay.name}</p></div>`);
        } else {
            modeElements.push(modes.selfplay.name);
        }
    }
    if (modes.pilot) {
        if (isExpired) {
            modeElements.push(`<div class="${modeClass}"><p>${modes.pilot.name}</p></div>`);
        } else {
            modeElements.push(modes.pilot.name);
        }
    }
    
    if (isExpired) {
        return modeElements.join('') || '<p>N/A</p>'; // Возвращаем HTML для Expired
    } else {
        return `<p>${modeElements.join(', ') || 'N/A'}</p>`; // Возвращаем текст для остальных
    }
}

// Вспомогательная функция для форматирования ячейки "Booked customers"
function formatBookedCustomers(bookedCustomers, customersCount, innerDivClass) {
    if (bookedCustomers && typeof bookedCustomers === 'object' && bookedCustomers.items && bookedCustomers.totalCount > 0) {
        // Генерируем HTML на основе примера
        const customerItemsHTML = bookedCustomers.items.map(item => 
            // Обертка для каждого ID
            `<div class="css-14wrgfm"><span class="css-1uwsgb6">${item.serviceId}</span></div>`
        ).join('');

        // HTML для стрелки (всегда показываем, если есть заказы, как в примере)
        const arrowHTML = `
            <div class="css-p08om6">
                <div class="css-17yvvz4">
                    <svg><use href="/front-ui/dist/pro/images/sprite.svg?4c5208fd9dc78683c7b139eb49bc4549#chevron-down"></use></svg>
                </div>
            </div>`;
            
        // Собираем всё вместе внутри основного div
        return `<div class="${innerDivClass}">${customerItemsHTML}${arrowHTML}</div>`;

    } else if (customersCount) {
        // Запасной вариант с customersCount
        return `<div class="${innerDivClass}"><p>${customersCount}</p></div>`; 
    } else {
        // Стандартный текст, если ничего нет
        return `<div class="${innerDivClass}"><p>None Yet</p></div>`;
    }
}

// Функция создания строки таблицы
export function createTableRow(run) {
    // Отладочный код для проверки свойств объекта run
    // console.log('Данные о рейде:', run);
    // console.log('Свойства связанные с клиентами:', {
    //     bookedCustomers: run.bookedCustomers,
    //     customersCount: run.customersCount,
    //     customers: run.customers,
    //     bookings: run.bookings
    // });
    
    const tbody = document.createElement('tbody');
    tbody.style.opacity = '1';
    
    const tr = document.createElement('tr');
    tr.setAttribute('data-testid', 'tr');
    tr.setAttribute('index', '0');
    
    // Определяем классы для ячеек в зависимости от статуса
    const isExpired = run.status.name === 'Expired';

    // Устанавливаем класс для TR
    // Если Expired, используем css-1tvl9qs, иначе - чередуем
    tr.className = isExpired ? 'css-1tvl9qs' : (run.id % 2 === 0 ? 'css-wub8ws' : 'css-1tvl9qs');
    
    const raidNameClass = isExpired ? 'css-il2zbt' : 'css-1ejlbpc';
    const timeClass = isExpired ? 'css-lim5rv' : 'css-1py4iwb';
    const contentClass = isExpired ? 'css-1m3ff6y' : 'css-3ibdlx';
    const modeClass = isExpired ? 'css-il2zbt' : 'css-3ibdlx'; 
    const tdClass = isExpired ? 'css-sg9cnb pivoted' : 'css-hqf6k5 pivoted'; 
    
    // Разбираем дату и время
    const dateTime = new Date(run.runDateTime);
    const date = dateTime.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
    const time = dateTime.toLocaleTimeString('en-US', { hour: '2-digit', minute: '2-digit', hour12: false });
    
    // Формируем HTML для строки
    tr.innerHTML = generateRowHTML(run, {
        isExpired,
        raidNameClass,
        timeClass,
        contentClass,
        modeClass,
        tdClass, 
        date,
        time
    });
    
    tbody.appendChild(tr);
    addCheckboxToRow(tr);
    
    // Добавляем обработчики для кнопок действий
    addActionHandlers(tbody, run.id);

    return tbody;
}

// Вспомогательная функция для генерации HTML строки
function generateRowHTML(run, params) {
    const {
        isExpired,
        raidNameClass,
        timeClass,
        contentClass, 
        modeClass,    
        tdClass,      
        date, // Этот date уже отформатирован как "Month Day"
        time  // Этот time уже отформатирован
    } = params;

    // Форматируем день с суффиксом (st, nd, rd, th)
    const dayOfMonth = new Date(run.runDateTime).getDate();
    let daySuffix = 'th';
    if (dayOfMonth % 10 === 1 && dayOfMonth !== 11) daySuffix = 'st';
    else if (dayOfMonth % 10 === 2 && dayOfMonth !== 12) daySuffix = 'nd';
    else if (dayOfMonth % 10 === 3 && dayOfMonth !== 13) daySuffix = 'rd';
    const formattedDate = `${date}${daySuffix}`; // Добавляем суффикс к дате

    // Определяем классы для внутренних div в зависимости от isExpired
    const innerDivClass = isExpired ? 'css-1m3ff6y' : 'css-3ibdlx';
    const innerDivClassMode = isExpired ? 'css-il2zbt' : 'css-3ibdlx'; // Для колонки Mode, если не раздельные div
    const regionDivClass = 'css-3ibdlx'; // Класс для div региона всегда одинаковый
    const statusDivClass = 'css-3ibdlx'; // Класс для div статуса всегда одинаковый
    const actionsDivClass = 'css-3ibdlx'; // Класс для div действий всегда одинаковый

    // Используем tdClass для большинства <td>, кроме первого (ID)
    return `
        <td data-testid="td" class="css-1iml5e0 pivoted"> 
            <div data-testid="td-before" class="tdBefore">ID</div>
            <div class="css-3ibdlx"><p>${run.id}</p></div>
        </td>
        
        <td data-testid="td" class="${tdClass}">
            <div data-testid="td-before" class="tdBefore">Region</div>
            <div class="${regionDivClass}"> 
                <div class="css-15bcuyv">
                    <img src="/front-ui/dist/pro/images/${run.region.id}-icon.png" alt="${run.region.name}">
                    <p>${run.region.name}</p>
                </div>
            </div>
        </td>
        
        <td data-testid="td" class="${tdClass}">
            <div data-testid="td-before" class="tdBefore">Raid name + difficulty</div>
            <div class="${raidNameClass}">
                <p>${run.name}</p>
                <span class="css-s2p2er">${run.difficulty.name}</span>
            </div>
        </td>
        
        <td data-testid="td" class="${tdClass}">
            <div data-testid="td-before" class="tdBefore">Time</div>
            <div class="${timeClass}">
                <div class="css-ftmei3">
                    <p>${formattedDate}</p>
                    <span>${time}</span>
                    <p>${run.runTimezone.name}</p>
                </div>
            </div>
        </td>
        
        <td data-testid="td" class="${tdClass}">
            <div data-testid="td-before" class="tdBefore">Run type</div>
            <div class="${innerDivClass}">
                ${run.lootType.name === 'Premium Loot' ? 
                    `<p><span class="css-11j0ltd">${run.lootType.name}</span></p>` : 
                    `<p>${run.lootType.name}</p>`}
                ${run.soloRun ? '<span>Solo only</span>' : ''}
                ${run.atp ? '<span>ATP only</span>' : ''}
            </div>
        </td>
        
        <td data-testid="td" class="${tdClass}">
            <div data-testid="td-before" class="tdBefore">Service</div>
            <div class="${innerDivClass}">
                ${formatServiceColumn(run.service)}
            </div>
        </td>
        
        <td data-testid="td" class="${tdClass}">
            <div data-testid="td-before" class="tdBefore">Mode</div>
            ${formatModes(run.modes, isExpired, modeClass)} 
        </td>
        
        <td data-testid="td" class="${tdClass}">
            <div data-testid="td-before" class="tdBefore">Unsaved raiders + allowed spots</div>
            <div class="${innerDivClass}">
                <p><strong>${run.unsavedRaiders}</strong></p>
                <span class="css-d1bq5k">${run.spots} </span>
            </div>
        </td>
        
        <td data-testid="td" class="${tdClass}">
            <div data-testid="td-before" class="tdBefore">Booked customers</div>
            ${formatBookedCustomers(run.bookedCustomers, run.customersCount, innerDivClass)} 
        </td>
        
        <td data-testid="td" class="${tdClass}">
            <div data-testid="td-before" class="tdBefore">Run status</div>
            <div class="${statusDivClass}">
                <span class="${getStatusClass(run.status.name)}">${run.status.name}</span>
            </div>
        </td>
        
        ${generateActionsHTML(run, isExpired, tdClass)} 
    `;
}

// Вспомогательная функция для генерации HTML кнопок действий
function generateActionsHTML(run, isExpired, tdClass) {
    // Используем tdClass для ячейки действий
    const actionsDivClass = 'css-3ibdlx'; // Класс для div действий всегда одинаковый
    return `
        <td data-testid="td" class="${tdClass}">
            <div data-testid="td-before" class="tdBefore">Actions</div>
            <div class="${actionsDivClass}">
                <div class="css-1erhubr">
                    <div class="css-z1qouo">
                        ${isExpired ? 
                            `<span disabled="" class="css-kitfve">
                                <svg><use href="/front-ui/dist/pro/images/sprite.svg?20a1b45fe38439b1bd0ff7f58d893bc2#resume"></use></svg>
                            </span>` :
                            `<span class="css-kitfve action-button" data-action="${run.status.name === 'Active' ? 'pause' : 'resume'}" data-id="${run.id}">
                                <svg><use href="/front-ui/dist/pro/images/sprite.svg?20a1b45fe38439b1bd0ff7f58d893bc2#${run.status.name === 'Active' ? 'pause' : 'resume'}"></use></svg>
                            </span>`
                        }
                        <span class="css-kitfve action-button" data-action="delete" data-id="${run.id}">
                            <svg><use href="/front-ui/dist/pro/images/sprite.svg?20a1b45fe38439b1bd0ff7f58d893bc2#trash"></use></svg>
                        </span>
                        <a class="css-kitfve" href="/booster/wow-raid-runs/${run.id}/edit">
                            <span class="css-kitfve">
                                <svg><use href="/front-ui/dist/pro/images/sprite.svg?20a1b45fe38439b1bd0ff7f58d893bc2#borderEdit"></use></svg>
                            </span>
                        </a>
                        <span class="css-kitfve action-button" data-action="view" data-id="${run.id}">
                            <svg><use href="/front-ui/dist/pro/images/sprite.svg?20a1b45fe38439b1bd0ff7f58d893bc2#search"></use></svg>
                        </span>
                    </div>
                </div>
            </div>
        </td>
    `;
}

// Вспомогательная функция для определения класса статуса
function getStatusClass(status) {
    switch(status) {
        case 'Active': return 'css-cjr0x0';  // Обновленный класс для Active
        case 'Paused': return 'css-vd2cvu';   // Обновленный класс для Paused
        case 'Expired': return 'css-1bo1kyo'; // Оставляем как было для Expired (или можно использовать css-vd2cvu?)
        default: return 'css-vd2cvu'; // Стандартный класс для неизвестных статусов
    }
}

// Функция добавления обработчиков действий
function addActionHandlers(tbody, id) {
    const actionButtons = tbody.querySelectorAll('.action-button');
    actionButtons.forEach(button => {
        button.addEventListener('click', async () => {
            const action = button.dataset.action;
            
            try {
                let response;
                switch(action) {
                    case 'pause':
                    case 'resume':
                        response = await fetch(`https://blazingboost.com/v1/raids/runs/${id}/${action}`, {
                            headers: {
                                "accept": "*/*",
                                "content-type": "application/json",
                                "sec-fetch-dest": "empty",
                                "sec-fetch-mode": "cors",
                                "sec-fetch-site": "same-origin"
                            },
                            referrer: window.location.href,
                            referrerPolicy: "strict-origin-when-cross-origin",
                            body: "\"\"",
                            method: "POST",
                            mode: "cors",
                            credentials: "include"
                        });
                        if (response.ok) {
                            updateActionButtonState(button, action);
                            updateStatusCell(tbody, action);
                        }
                        break;
                        
                    case 'delete':
                        if (confirm('Вы уверены, что хотите удалить это событие?')) {
                            const success = await deleteEvent(id);
                            if (success) {
                                tbody.remove();
                            }
                        }
                        break;
                        
                    case 'view':
                        const data = await getEventDetails(id);
                        if (data) {
                            createModalWindow(data);
                        }
                        break;
                }
            } catch (error) {
                console.error(`[BB Ext] Ошибка при выполнении действия ${action}:`, error);
            }
        });
    });
}

// Вспомогательная функция для обновления состояния кнопки действия
function updateActionButtonState(button, action) {
    const svg = button.querySelector('svg use');
    const newAction = action === 'pause' ? 'resume' : 'pause';
    svg.setAttribute('href', `/front-ui/dist/pro/images/sprite.svg?20a1b45fe38439b1bd0ff7f58d893bc2#${newAction}`);
    button.dataset.action = newAction;
}

// Вспомогательная функция для обновления ячейки статуса
function updateStatusCell(tbody, action) {
    const row = tbody.querySelector('[data-testid="tr"]');
    const statusCell = Array.from(row.querySelectorAll('[data-testid="td"]'))
        .find(td => td.querySelector('.tdBefore')?.textContent === 'Run status')
        ?.querySelector('.css-3ibdlx span');

    if (statusCell) {
        const newStatus = action === 'pause' ? 'Paused' : 'Active';
        statusCell.textContent = newStatus;
        statusCell.className = getStatusClass(newStatus);
    }
}