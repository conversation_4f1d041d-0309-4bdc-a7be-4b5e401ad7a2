// Импортируем утилиты
import { log } from './utils.js';

// Функция форматирования даты для модального окна
function formatDate(dateString, timezone) {
    try {
        if (!dateString) {
            log('Дата отсутствует');
            return 'N/A';
        }
        
        const date = new Date(dateString.replace(' ', 'T'));
        if (isNaN(date.getTime())) {
            log('Некорректная дата:', dateString);
            return 'N/A';
        }
        
        const month = date.toLocaleString('en-US', { month: 'long' });
        const day = date.getDate();
        
        // Правильное определение суффикса
        let suffix = 'th';
        if (day % 10 === 1 && day !== 11) {
            suffix = 'st';
        } else if (day % 10 === 2 && day !== 12) {
            suffix = 'nd';
        } else if (day % 10 === 3 && day !== 13) {
            suffix = 'rd';
        }
        
        const time = date.toLocaleTimeString('en-US', { 
            hour: '2-digit', 
            minute: '2-digit', 
            hour12: false 
        });
        
        return `${month} ${day}${suffix}  -  ${time} ${timezone || 'CET'}`;
    } catch (error) {
        log('Ошибка форматирования даты:', error);
        return 'N/A';
    }
}

// Функция создания модального окна
export function createModalWindow(data) {
    // Создаем элементы модального окна
    const modal = document.createElement('div');
    modal.className = 'css-wp0xt3';
    modal.tabIndex = '-1';
    modal.style.cssText = `
        opacity: 1;
        transition: opacity 225ms cubic-bezier(0.4, 0, 0.2, 1);
        position: fixed;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        background: rgb(24, 26, 27);
        padding: 20px;
        border-radius: 8px;
        z-index: 1001;
        min-width: 400px;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
        color: #fff;
    `;

    // Логируем данные для отладки
    log('Форматирование дат:', {
        runAt: data.runAt,
        postedAt: data.postedAt,
        formattedRunAt: formatDate(data.runAt, data.runTimezone?.name),
        formattedPostedAt: formatDate(data.postedAt, data.runTimezone?.name)
    });

    // Добавляем заголовок и кнопку закрытия
    modal.innerHTML = `
        <h3 class="css-1j1a9hd" style="color: #fff;">View run summary</h3>
        <button class="css-lyeuba">
            <svg style="fill: #fff;"><use href="/front-ui/dist/pro/images/sprite.svg?20a1b45fe38439b1bd0ff7f58d893bc2#delete"></use></svg>
        </button>
        <div class="css-1vlottr">
            <div class="css-1gek93m">
                <div class="css-1cw614e">
                    <span class="css-1hlssd1" style="color: #999;"><strong>Date of run:</strong></span>
                    <span class="css-1fe8tda" style="color: #fff;">${formatDate(data.runAt, data.runTimezone?.name)}</span>
                </div>
                <div class="css-1cw614e">
                    <span class="css-agna0h" style="color: #999;"><strong>Date of posting:</strong></span>
                    <span class="css-18fknow" style="color: #fff;">${formatDate(data.postedAt, data.runTimezone?.name)}</span>
                </div>
                <div class="css-1cw614e"></div>
                <div class="css-1cw614e">
                    <span class="css-1hlssd1" style="color: #999;"><strong>Region:</strong></span>
                    <span class="css-1ooh6uu" style="color: #fff;">${data.region?.name || 'N/A'}</span>
                </div>
                <div class="css-1cw614e">
                    <span class="css-1hlssd1" style="color: #999;"><strong>Raid:</strong></span>
                    <span class="css-1ooh6uu" style="color: #fff;">${data.name || 'N/A'}</span>
                </div>
                <div class="css-1cw614e">
                    <span class="css-1hlssd1" style="color: #999;"><strong>Difficulty:</strong></span>
                    <span class="css-1ooh6uu" style="color: #fff;">${data.difficulty?.name || 'N/A'}</span>
                </div>
                <div class="css-1cw614e">
                    <span class="css-1hlssd1" style="color: #999;"><strong>Run type:</strong></span>
                    <span class="css-1ooh6uu" style="color: #fff;">${data.lootType?.name || 'N/A'}</span>
                </div>
                <div class="css-1cw614e">
                    <span class="css-1hlssd1" style="color: #999;"><strong>Service:</strong></span>
                    <span class="css-1ooh6uu" style="color: #fff;">${data.service || 'N/A'}</span>
                </div>
                <div class="css-1cw614e">
                    <span class="css-1hlssd1" style="color: #999;"><strong>Allowed Modes:</strong></span>
                    <span class="css-1ooh6uu" style="color: #fff;">${data.modes?.selfplay?.name || 'N/A'}</span>
                </div>
                <div class="css-1cw614e">
                    <span class="css-1hlssd1" style="color: #999;"><strong>Unsaved raiders:</strong></span>
                    <span class="css-1ooh6uu" style="color: #fff;">${data.unsavedRaiders || 'N/A'}</span>
                </div>
            </div>
        </div>
    `;

    // Добавляем затемнение фона
    const overlay = document.createElement('div');
    overlay.style.cssText = 'position: fixed; top: 0; left: 0; right: 0; bottom: 0; background: rgba(0, 0, 0, 0.7); z-index: 1000;';

    // Добавляем обработчики закрытия
    const closeModal = () => {
        modal.style.opacity = '0';
        overlay.style.opacity = '0';
        setTimeout(() => {
            modal.remove();
            overlay.remove();
        }, 225);
    };

    overlay.addEventListener('click', closeModal);
    modal.querySelector('.css-lyeuba').addEventListener('click', closeModal);

    // Добавляем элементы на страницу
    document.body.appendChild(overlay);
    document.body.appendChild(modal);
} 