// Импортируем утилиты логирования
import { log } from './utils.js';

// Функция для удаления события
export async function deleteEvent(id) {
    log(`Попытка удаления события с ID: ${id}`);
    try {
        const response = await fetch(`https://blazingboost.com/v1/raids/runs/${id}/delete`, {
            headers: {
                "accept": "*/*",
                "content-type": "application/json",
                "sec-fetch-dest": "empty",
                "sec-fetch-mode": "cors",
                "sec-fetch-site": "same-origin"
            },
            referrer: "https://blazingboost.com/booster/wow-raid-runs",
            referrerPolicy: "strict-origin-when-cross-origin",
            body: "\"\"",
            method: "POST",
            mode: "cors",
            credentials: "include"
        });
        
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        log(`Успешно удалено событие с ID: ${id}`);
        return true;
    } catch (error) {
        log(`Ошибка при удалении события ${id}:`, error);
        return false;
    }
}

// Функция для загрузки данных страницы
export async function loadPageData(page, difficulty) {
    const url = `https://blazingboost.com/v1/raids/runs?region=eu&difficulty=${difficulty}&sort=next&page=${page}`;
    try {
        const response = await fetch(url, {
            headers: {
                "accept": "*/*",
                "sec-fetch-dest": "empty",
                "sec-fetch-mode": "cors",
                "sec-fetch-site": "same-origin"
            },
            referrer: window.location.href,
            referrerPolicy: "strict-origin-when-cross-origin",
            method: "GET",
            mode: "cors",
            credentials: "include"
        });

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status} for ${url}`);
        }

        return await response.json();
    } catch (error) {
        log(`Ошибка при загрузке страницы: ${url}`, error);
        return null;
    }
}

// Функция для изменения статуса события (pause/resume)
export async function changeEventStatus(id, action) {
    try {
        const response = await fetch(`https://blazingboost.com/v1/raids/runs/${id}/${action}`, {
            headers: {
                "accept": "*/*",
                "content-type": "application/json",
                "sec-fetch-dest": "empty",
                "sec-fetch-mode": "cors",
                "sec-fetch-site": "same-origin"
            },
            referrer: window.location.href,
            referrerPolicy: "strict-origin-when-cross-origin",
            body: "\"\"",
            method: "POST",
            mode: "cors",
            credentials: "include"
        });
        
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        
        return true;
    } catch (error) {
        log(`Ошибка при изменении статуса события ${id}:`, error);
        return false;
    }
}

// Функция для получения детальной информации о событии
export async function getEventDetails(id) {
    try {
        const response = await fetch(`https://blazingboost.com/v1/raids/runs/${id}`, {
            headers: {
                "accept": "*/*",
                "sec-fetch-dest": "empty",
                "sec-fetch-mode": "cors",
                "sec-fetch-site": "same-origin"
            },
            referrer: window.location.href,
            referrerPolicy: "strict-origin-when-cross-origin",
            method: "GET",
            mode: "cors",
            credentials: "include"
        });
        
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        
        return await response.json();
    } catch (error) {
        log(`Ошибка при получении деталей события ${id}:`, error);
        return null;
    }
} 