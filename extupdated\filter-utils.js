// Импортируем утилиты логирования
import { log } from './utils.js';

// Глобальная переменная для отслеживания состояния загрузки
let isLoadingComplete = false;

// Функция для установки состояния загрузки
export function setLoadingComplete(complete) {
    isLoadingComplete = complete;
    // Если загрузка завершена, обновляем список времен
    if (complete) {
        const container = document.querySelector('.bb-filter-panel');
        if (container) {
            updateTimesList(container);
        }
    }
}

// Функция для получения уникальных времен из таблицы
function getUniqueTimesFromTable() {
    const times = new Set();
    const rows = document.querySelectorAll('[data-testid="table"] tbody tr');
    
    log('Найдено строк для извлечения времени:', rows.length);
    
    rows.forEach(row => {
        // Ищем ячейку с временем по классу
        const timeCell = row.querySelector('.css-ftmei3 span');
        if (timeCell) {
            const timeText = timeCell.textContent.trim();
            log('Найденное время:', timeText);
            if (timeText) {
                times.add(timeText);
                log('Добавлено время:', timeText);
            }
        }
    });
    
    const sortedTimes = Array.from(times).sort();
    log('Полученные времена:', sortedTimes);
    return sortedTimes;
}

// Функция обновления списка времен
function updateTimesList(container) {
    const timesList = container.querySelector('.bb-time-list');
    if (!timesList) return;

    // Функция проверки загрузки страниц
    const checkLoadingComplete = () => {
        // Проверяем, есть ли сообщение о пустой таблице
        const emptyMessage = document.querySelector('.css-tg784k');
        if (emptyMessage && emptyMessage.textContent.includes("You don't have any data yet")) {
            log('Таблица пуста, прекращаем проверку');
            timesList.innerHTML = '<div class="bb-time-empty">Нет доступных событий</div>';
            return;
        }

        if (!isLoadingComplete) {
            // Если загрузка не завершена, ждем
            log('Ожидаем завершения загрузки всех страниц...');
            setTimeout(checkLoadingComplete, 1000);
            return;
        }

        // Получаем времена из таблицы
        const uniqueTimes = getUniqueTimesFromTable();
        if (uniqueTimes.length === 0) {
            const table = document.querySelector('[data-testid="table"]');
            if (!table) {
                log('Таблица не найдена');
                return;
            }
            
            const rows = table.querySelectorAll('tbody tr');
            if (rows.length === 0) {
                log('Строки не найдены');
                return;
            }
            
            log('Времена не найдены');
            return;
        }

        // Обновляем список времен
        timesList.innerHTML = uniqueTimes.map(time => `
            <label class="bb-filter-checkbox-label">
                <input type="checkbox" class="bb-filter-checkbox" data-filter="time" value="${time}">
                <span>${time}</span>
            </label>
        `).join('');

        // Обновляем обработчики
        timesList.querySelectorAll('.bb-filter-checkbox').forEach(checkbox => {
            checkbox.addEventListener('change', applyFilters);
        });

        log('Список времен обновлен после полной загрузки');
    };

    // Запускаем проверку
    checkLoadingComplete();
}

// Функция создания фильтра времени
function createTimeFilter() {
    const uniqueTimes = getUniqueTimesFromTable();
    log('Уникальные времена из таблицы:', uniqueTimes);

    const timeGroup = document.createElement('div');
    timeGroup.className = 'bb-filter-group';
    timeGroup.innerHTML = `
        <label class="bb-filter-label">Time</label>
        <select class="bb-filter-select" data-filter="time">
            <option value="">All</option>
            ${uniqueTimes.map(time => `<option value="${time}">${time}</option>`).join('')}
        </select>
    `;
    
    return timeGroup;
}

// Функция создания панели фильтров
export function createFilterPanel() {
    const panel = document.createElement('div');
    panel.className = 'bb-filter-panel';
    
    // Функция для создания группы фильтров
    const createFilterGroup = (title, items, filterName) => {
        const group = document.createElement('div');
        group.className = 'bb-filter-section';
        
        group.innerHTML = `
            <div class="bb-filter-title">${title}</div>
            <div class="bb-filter-checkboxes" id="${filterName}Group">
                ${items.map(item => `
                    <label class="bb-filter-checkbox-label">
                        <input type="checkbox" class="bb-filter-checkbox" 
                            data-filter="${filterName}" 
                            value="${item.value}" 
                            ${item.checked ? 'checked' : ''}>
                        <span>${item.label}</span>
                    </label>
                `).join('')}
            </div>
        `;
        
        return group;
    };

    // Создаем группы фильтров
    const statusGroup = createFilterGroup('Статус', [
        { value: 'Active', label: 'Active' },
        { value: 'Paused', label: 'Paused' },
        { value: 'Expired', label: 'Expired' }
    ], 'status');

    const serviceGroup = createFilterGroup('Сервис', [
        { value: 'Full Clear', label: 'Full Clear' },
        { value: 'Last Boss Kill', label: 'Last Boss Kill' },
        { value: 'Last 2 Boss Kills', label: 'Last 2 Boss Kills' },
        { value: 'Flexible Clear', label: 'Flexible Clear' }
    ], 'service');

    const difficultyGroup = createFilterGroup('Сложность', [
        { value: 'Normal', label: 'Normal' },
        { value: 'Heroic', label: 'Heroic' },
        { value: 'Mythic', label: 'Mythic' }
    ], 'difficulty');

    const runTypeGroup = createFilterGroup('Run Type', [
        { value: 'Loot Sharing', label: 'Loot Sharing' },
        { value: 'Premium Loot', label: 'Premium Loot' }
    ], 'runType');

    // Создаем группу дат
    const dateGroup = document.createElement('div');
    dateGroup.className = 'bb-filter-section';
    
    dateGroup.innerHTML = `
        <div class="bb-filter-title">Даты</div>
        <div class="bb-date-inputs">
            <div class="bb-date-group">
                <label>От:</label>
                <input type="date" id="dateFrom" class="bb-date-input">
            </div>
            <div class="bb-date-group">
                <label>До:</label>
                <input type="date" id="dateTo" class="bb-date-input">
            </div>
        </div>
        <div class="bb-time-filter">
            <div class="bb-time-select">
                <button class="bb-time-button">Выбрать время</button>
                <div class="bb-time-dropdown">
                    <div class="bb-time-header">
                        <label class="bb-filter-checkbox-label">
                            <input type="checkbox" class="bb-filter-checkbox" id="selectAllTimes">
                            <span>Выбрать все</span>
                        </label>
                    </div>
                    <div class="bb-time-list">
                        <div class="bb-time-loading">Загрузка времен...</div>
                    </div>
                </div>
            </div>
        </div>
    `;

    // Запускаем обновление списка времен после создания структуры
    setTimeout(() => updateTimesList(dateGroup), 1000);

    // Создаем группу сортировки
    const sortGroup = document.createElement('div');
    sortGroup.className = 'bb-filter-section';
    sortGroup.innerHTML = `
        <div class="bb-filter-title">Сортировка</div>
        <div class="bb-sort-controls">
            <select class="bb-filter-select" id="sortBy" style="background: rgb(24, 26, 27); color: #fff; border: 1px solid #444; padding: 5px 10px; border-radius: 4px;">
                <option value="">Без сортировки</option>
                <option value="id">ID</option>
                <option value="date">Дата</option>
                <option value="status">Статус</option>
            </select>
            <select class="bb-filter-select" id="sortDirection" style="background: rgb(24, 26, 27); color: #fff; border: 1px solid #444; padding: 5px 10px; border-radius: 4px; margin-left: 5px;">
                <option value="asc">По возрастанию</option>
                <option value="desc">По убыванию</option>
            </select>
        </div>
    `;

    // Создаем кнопки управления
    const controls = document.createElement('div');
    controls.className = 'bb-filter-controls';
    controls.innerHTML = `
        <button class="bb-filter-button primary" id="loadAllPages">Загрузить все страницы</button>
        <button class="bb-filter-button danger" id="deleteSelected">Удалить выбранное</button>
        <button class="bb-filter-button secondary" id="resetFilters">Сбросить</button>
    `;

    // Собираем панель
    const container = document.createElement('div');
    container.className = 'bb-filter-container';
    container.appendChild(statusGroup);
    container.appendChild(serviceGroup);
    container.appendChild(difficultyGroup);
    container.appendChild(runTypeGroup);
    container.appendChild(dateGroup);
    container.appendChild(sortGroup);

    panel.appendChild(container);
    panel.appendChild(controls);

    // Добавляем обработчики событий
    panel.querySelector('#resetFilters').addEventListener('click', resetFilters);
    panel.querySelector('#loadAllPages').addEventListener('click', () => {
        const loadMoreButton = document.querySelector('.bb-load-more-button');
        if (loadMoreButton) {
            loadMoreButton.click();
        }
    });
    panel.querySelector('#deleteSelected').addEventListener('click', () => {
        const deleteButton = document.querySelector('.bb-delete-button');
        if (deleteButton) {
            deleteButton.click();
        }
    });
    
    // Добавляем обработчики для мгновенного применения фильтров
    panel.querySelectorAll('.bb-filter-checkbox').forEach(checkbox => {
        checkbox.addEventListener('change', applyFilters);
    });
    
    panel.querySelector('#dateFrom').addEventListener('change', applyFilters);
    panel.querySelector('#dateTo').addEventListener('change', applyFilters);
    
    panel.querySelector('#sortBy').addEventListener('change', applySort);
    panel.querySelector('#sortDirection').addEventListener('change', applySort);

    // Добавляем обработчики для выпадающего списка времени
    dateGroup.querySelector('.bb-time-button').addEventListener('click', (e) => {
        e.stopPropagation();
        const dropdown = dateGroup.querySelector('.bb-time-dropdown');
        dropdown.classList.toggle('show');
        
        // Закрываем при клике вне dropdown
        const closeDropdown = (event) => {
            if (!dropdown.contains(event.target) && !e.target.contains(event.target)) {
                dropdown.classList.remove('show');
                document.removeEventListener('click', closeDropdown);
            }
        };
        document.addEventListener('click', closeDropdown);
    });

    // Обработчик для "Выбрать все" времена
    dateGroup.querySelector('#selectAllTimes').addEventListener('change', (e) => {
        dateGroup.querySelectorAll('[data-filter="time"]').forEach(cb => {
            cb.checked = e.target.checked;
        });
        applyFilters();
    });

    return panel;
}

// Функция применения фильтров
export function applyFilters() {
    log('Применяем фильтры...');
    
    const rows = document.querySelectorAll('[data-testid="table"] tbody tr');
    const filters = {
        status: Array.from(document.querySelectorAll('[data-filter="status"]:checked')).map(cb => cb.value),
        service: Array.from(document.querySelectorAll('[data-filter="service"]:checked')).map(cb => cb.value),
        difficulty: Array.from(document.querySelectorAll('[data-filter="difficulty"]:checked')).map(cb => cb.value),
        runType: Array.from(document.querySelectorAll('[data-filter="runType"]:checked')).map(cb => cb.value),
        time: Array.from(document.querySelectorAll('[data-filter="time"]:checked')).map(cb => cb.value),
        dateFrom: document.querySelector('#dateFrom').value,
        dateTo: document.querySelector('#dateTo').value
    };

    log('Активные фильтры:', filters);

    rows.forEach(row => {
        let show = true;

        // Проверяем статус
        if (filters.status.length > 0) {
            // Ищем все ячейки с tdBefore
            const cells = row.querySelectorAll('[data-testid="td"] .tdBefore');
            let status = null;
            
            // Ищем ячейку с Run status
            cells.forEach(cell => {
                if (cell.textContent.trim() === 'Run status') {
                    status = cell.parentElement.querySelector('.css-3ibdlx span')?.textContent.trim();
                    log('Найден статус в строке:', status);
                }
            });
            
            if (status) {
                show = show && filters.status.includes(status);
                log(`Проверка статуса: ${status}, включен в фильтры: ${filters.status.includes(status)}`);
            } else {
                log('Статус не найден в строке');
                show = false;
            }
        }

        // Проверяем сервис
        if (filters.service.length > 0) {
            // Ищем все ячейки с tdBefore
            const cells = row.querySelectorAll('[data-testid="td"] .tdBefore');
            let service = null;
            
            // Ищем ячейку с Service
            cells.forEach(cell => {
                if (cell.textContent.trim() === 'Service') {
                    // Проверяем оба возможных класса
                    const serviceText = cell.parentElement.querySelector('.css-3ibdlx p, .css-1m3ff6y p')?.textContent.trim();
                    if (serviceText) {
                        service = serviceText;
                        log('Найден Service в строке:', service);
                    }
                }
            });
            
            if (service) {
                show = show && filters.service.includes(service);
                log(`Проверка Service: ${service}, включен в фильтры: ${filters.service.includes(service)}`);
            } else {
                log('Service не найден в строке');
                show = false;
            }
        }

        // Проверяем сложность
        if (filters.difficulty.length > 0) {
            const difficulty = row.querySelector('.css-s2p2er')?.textContent.trim();
            show = show && filters.difficulty.includes(difficulty);
        }

        // Проверяем Run Type
        if (filters.runType.length > 0) {
            // Ищем все ячейки с tdBefore
            const cells = row.querySelectorAll('[data-testid="td"] .tdBefore');
            let runType = null;
            
            // Ищем ячейку с Run type
            cells.forEach(cell => {
                if (cell.textContent.trim() === 'Run type') {
                    // Проверяем оба возможных класса
                    const runTypeText = cell.parentElement.querySelector('.css-3ibdlx p, .css-1m3ff6y p')?.textContent.trim();
                    if (runTypeText) {
                        runType = runTypeText;
                        log('Найден Run Type в строке:', runType);
                    }
                }
            });
            
            if (runType) {
                show = show && filters.runType.includes(runType);
                log(`Проверка Run Type: ${runType}, включен в фильтры: ${filters.runType.includes(runType)}`);
            } else {
                log('Run Type не найден в строке');
                show = false;
            }
        }

        // Проверяем время
        if (filters.time.length > 0) {
            const timeCell = row.querySelector('.css-ftmei3 span');
            if (timeCell) {
                const timeText = timeCell.textContent.trim();
                log('Проверяем время:', timeText, 'против фильтров:', filters.time);
                show = show && filters.time.includes(timeText);
            } else {
                show = false;
            }
        }

        // Проверяем диапазон дат
        if (filters.dateFrom || filters.dateTo) {
            const dateCell = row.querySelector('.css-ftmei3');
            if (dateCell) {
                const month = dateCell.querySelector('p:first-child')?.textContent;
                const time = dateCell.querySelector('span')?.textContent;
                const year = new Date().getFullYear();
                
                if (month && time) {
                    const [monthStr, day] = month.split(' ');
                    const [hours, minutes] = time.split(':');
                    const rowDate = new Date(year, new Date(monthStr + ' 1, 2024').getMonth(), parseInt(day), 
                                        parseInt(hours), parseInt(minutes));
                    
                    if (filters.dateFrom) {
                        const fromDate = new Date(filters.dateFrom);
                        show = show && rowDate >= fromDate;
                    }
                    
                    if (filters.dateTo) {
                        const toDate = new Date(filters.dateTo);
                        toDate.setHours(23, 59, 59, 999);
                        show = show && rowDate <= toDate;
                    }
                }
            }
        }

        // Применяем видимость
        const tbody = row.closest('tbody');
        if (tbody) {
            row.style.display = show ? '' : 'none';
            
            // Если строка скрыта, снимаем чекбокс
            const checkbox = tbody.querySelector('.bb-checkbox');
            if (checkbox && !show) {
                checkbox.checked = false;
            }
        }
        log('Строка:', row.querySelector('[data-testid="td"] .css-3ibdlx p')?.textContent, 'видимость:', show);
    });

    // Обновляем состояние чекбокса "выделить все"
    const selectAllCheckbox = document.querySelector('#selectAll');
    if (selectAllCheckbox) {
        const visibleCheckboxes = Array.from(document.querySelectorAll('.bb-checkbox:not(#selectAll)')).filter(checkbox => {
            const row = checkbox.closest('tbody');
            return row && row.querySelector('tr').style.display !== 'none';
        });
        const checkedVisibleCheckboxes = visibleCheckboxes.filter(checkbox => checkbox.checked);
        selectAllCheckbox.checked = visibleCheckboxes.length > 0 && checkedVisibleCheckboxes.length === visibleCheckboxes.length;
    }

    // Обновляем видимость кнопок удаления
    const deleteButton = document.querySelector('.bb-delete-button');
    const deleteSelectedButton = document.querySelector('#deleteSelected');
    const checkedBoxes = document.querySelectorAll('.bb-checkbox:checked:not(#selectAll)');
    log(`Выбрано чекбоксов после применения фильтров: ${checkedBoxes.length}`);
    
    if (deleteButton) {
        deleteButton.style.visibility = checkedBoxes.length > 0 ? 'visible' : 'hidden';
    }
    if (deleteSelectedButton) {
        deleteSelectedButton.classList.toggle('visible', checkedBoxes.length > 0);
    }

    log('Фильтры применены');
}

// Функция применения сортировки
export function applySort() {
    const sortBy = document.querySelector('#sortBy').value;
    const direction = document.querySelector('#sortDirection').value;
    if (!sortBy) return;
    
    const table = document.querySelector('[data-testid="table"]');
    const tbody = table.querySelectorAll('tbody');
    const rowsArray = Array.from(tbody);
    
    rowsArray.sort((a, b) => {
        const aRow = a.querySelector('tr');
        const bRow = b.querySelector('tr');
        let comparison = 0;
        
        switch(sortBy) {
            case 'id':
                const aId = parseInt(aRow.querySelector('[data-testid="td"] .css-3ibdlx p')?.textContent || '0');
                const bId = parseInt(bRow.querySelector('[data-testid="td"] .css-3ibdlx p')?.textContent || '0');
                comparison = aId - bId;
                break;
                
            case 'date':
                const getDateFromCell = (row) => {
                    const dateCell = row.querySelector('.css-ftmei3');
                    if (dateCell) {
                        const month = dateCell.querySelector('p:first-child')?.textContent;
                        const time = dateCell.querySelector('span')?.textContent;
                        const year = new Date().getFullYear();
                        
                        if (month && time) {
                            const [monthStr, day] = month.split(' ');
                            const [hours, minutes] = time.split(':');
                            return new Date(year, new Date(monthStr + ' 1, 2024').getMonth(), parseInt(day), 
                                          parseInt(hours), parseInt(minutes));
                        }
                    }
                    return new Date(0);
                };
                
                const aDate = getDateFromCell(aRow);
                const bDate = getDateFromCell(bRow);
                comparison = aDate - bDate;
                break;
                
            case 'status':
                const aStatus = aRow.querySelector('[data-testid="td"] .css-3ibdlx span:last-child')?.textContent || '';
                const bStatus = bRow.querySelector('[data-testid="td"] .css-3ibdlx span:last-child')?.textContent || '';
                comparison = aStatus.localeCompare(bStatus);
                break;
        }
        
        return direction === 'desc' ? -comparison : comparison;
    });
    
    // Применяем сортировку
    rowsArray.forEach(row => table.appendChild(row));
}

// Функция сброса фильтров
export function resetFilters() {
    log('Сбрасываем фильтры...');
    
    // Сбрасываем чекбоксы, кроме чекбокса автозагрузки
    document.querySelectorAll('.bb-filter-checkbox:not(#autoloadCheckbox)').forEach(checkbox => {
        checkbox.checked = false;
    });

    // Сбрасываем даты
    const dateFrom = document.querySelector('#dateFrom');
    const dateTo = document.querySelector('#dateTo');
    if (dateFrom) dateFrom.value = '';
    if (dateTo) dateTo.value = '';

    // Показываем все строки
    document.querySelectorAll('[data-testid="table"] tbody tr').forEach(row => {
        row.style.display = '';
    });

    // Устанавливаем сортировку по дате по убыванию
    const sortBy = document.querySelector('#sortBy');
    const sortDirection = document.querySelector('#sortDirection');
    if (sortBy) sortBy.value = 'date';
    if (sortDirection) sortDirection.value = 'desc';
    
    // Применяем сортировку
    applySort();

    log('Фильтры сброшены, таблица отсортирована по дате по убыванию');
} 